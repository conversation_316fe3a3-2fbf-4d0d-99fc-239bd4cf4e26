from data_handler import load_las_files_from_directory, clean_log_data, write_results_to_las
from config_handler import get_io_paths, configure_log_selection, configure_well_separation, get_prediction_mode, configure_hyperparameters
from ml_core import impute_logs, MODEL_REGISTRY
from reporting import generate_qc_report, create_summary_plots, generate_final_report

def run():
    inp, out = get_io_paths()
    df, las_objs, wells, logs = load_las_files_from_directory(inp)
    if df.empty:
        return
    feats, tgt = configure_log_selection(logs)
    cfg = configure_well_separation(wells)
    mode = get_prediction_mode()
    hparams = configure_hyperparameters()

    clean_df = clean_log_data(df)
    generate_qc_report(clean_df, feats+[tgt], cfg)

    models = {MODEL_REGISTRY[k]['name']: MODEL_REGISTRY[k]['model_class'](**hparams[k]) for k in MODEL_REGISTRY}
    res_df, mres = impute_logs(clean_df, feats, tgt, models, cfg, mode)
    if not mres: return

    create_summary_plots(res_df, mres, cfg)
    write_results_to_las(res_df, tgt, las_objs, out)
    generate_final_report(mres, hparams)

if __name__=="__main__":
    run()
