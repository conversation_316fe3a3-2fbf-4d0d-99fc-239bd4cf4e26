#!/usr/bin/env python3
"""
Test the fixed data_handler with actual LAS files.
"""

from data_handler import load_las_files_from_directory
import os

def test_with_directory():
    """Test loading LAS files from directory."""
    test_dir = "C:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/BKP Main Log/RP_OUTPUT"
    
    if not os.path.exists(test_dir):
        print(f"Test directory not found: {test_dir}")
        return
    
    print(f"Testing directory: {test_dir}")
    print("=" * 60)
    
    df, las_objs, wells, logs = load_las_files_from_directory(test_dir)
    
    if not df.empty:
        print(f"✅ SUCCESS! Loaded data from directory:")
        print(f"   - Total data points: {len(df)}")
        print(f"   - Number of wells: {len(wells)}")
        print(f"   - Wells: {wells}")
        print(f"   - Number of log curves: {len(logs)}")
        print(f"   - Log curves: {logs[:10]}{'...' if len(logs) > 10 else ''}")
        print(f"   - DataFrame columns: {list(df.columns)}")
    else:
        print("❌ Failed to load data from directory")

def test_with_file_list():
    """Test loading specific LAS files."""
    test_files = [
        "C:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/BKP Main Log/RP_OUTPUT/B-G-6_RP_INPUT.las",
        "C:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/BKP Main Log/RP_OUTPUT/B-G-10_RP_INPUT.las"
    ]
    
    # Filter to only existing files
    existing_files = [f for f in test_files if os.path.exists(f)]
    
    if not existing_files:
        print("No test files found")
        return
    
    print(f"Testing with {len(existing_files)} specific files:")
    for f in existing_files:
        print(f"   - {os.path.basename(f)}")
    print("=" * 60)
    
    df, las_objs, wells, logs = load_las_files_from_directory(existing_files)
    
    if not df.empty:
        print(f"✅ SUCCESS! Loaded data from file list:")
        print(f"   - Total data points: {len(df)}")
        print(f"   - Number of wells: {len(wells)}")
        print(f"   - Wells: {wells}")
        print(f"   - Number of log curves: {len(logs)}")
        print(f"   - Log curves: {logs[:10]}{'...' if len(logs) > 10 else ''}")
    else:
        print("❌ Failed to load data from file list")

if __name__ == "__main__":
    print("Testing Fixed Data Handler")
    print("=" * 60)
    
    print("\n1. Testing directory loading:")
    test_with_directory()
    
    print("\n2. Testing file list loading:")
    test_with_file_list()
    
    print("\n✅ Testing completed!")
