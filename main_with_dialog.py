#!/usr/bin/env python3
"""
Enhanced main script with GUI file selection dialog for LAS files.
"""

from data_handler import load_las_files_from_directory, clean_log_data, write_results_to_las
from config_handler import get_io_paths_simple, configure_log_selection, configure_well_separation, get_prediction_mode, configure_hyperparameters
from ml_core import impute_logs, MODEL_REGISTRY
from reporting import generate_qc_report, create_summary_plots, generate_final_report

def run_with_dialog():
    """Run the ML log prediction workflow with GUI file selection."""
    print("=" * 60)
    print(" ML LOG PREDICTION - Enhanced with File Dialog")
    print("=" * 60)
    
    # Step 1: Get input/output paths using file dialog
    print("\n📁 Step 1: Select LAS files and output directory")
    inp, out = get_io_paths_simple()
    
    if not inp or not out:
        print("❌ File selection cancelled. Exiting.")
        return
    
    # Step 2: Load LAS files
    print("\n📊 Step 2: Loading LAS files...")
    df, las_objs, wells, logs = load_las_files_from_directory(inp)
    
    if df.empty:
        print("❌ No data loaded. Exiting.")
        return
    
    print(f"✅ Successfully loaded:")
    print(f"   • {len(df)} data points")
    print(f"   • {len(wells)} wells: {', '.join(wells)}")
    print(f"   • {len(logs)} log curves: {', '.join(logs)}")
    
    # Step 3: Configure log selection
    print("\n🎯 Step 3: Configure feature and target logs")
    feats, tgt = configure_log_selection(logs)
    print(f"✅ Feature logs: {', '.join(feats)}")
    print(f"✅ Target log: {tgt}")
    
    # Step 4: Configure well separation
    print("\n🏗️ Step 4: Configure training/prediction strategy")
    cfg = configure_well_separation(wells)
    print(f"✅ Mode: {cfg['mode']}")
    
    # Step 5: Configure prediction mode
    print("\n⚙️ Step 5: Configure prediction mode")
    mode = get_prediction_mode()
    print(f"✅ Prediction mode: {mode}")
    
    # Step 6: Configure hyperparameters
    print("\n🔧 Step 6: Configure model hyperparameters")
    hparams = configure_hyperparameters()
    print("✅ Using default hyperparameters for all models")
    
    # Step 7: Data cleaning and QC
    print("\n🧹 Step 7: Data cleaning and quality control")
    clean_df = clean_log_data(df)
    generate_qc_report(clean_df, feats+[tgt], cfg)
    
    # Step 8: Machine learning prediction
    print("\n🤖 Step 8: Running machine learning models...")
    models = {MODEL_REGISTRY[k]['name']: MODEL_REGISTRY[k]['model_class'](**hparams[k]) for k in MODEL_REGISTRY}
    res_df, mres = impute_logs(clean_df, feats, tgt, models, cfg, mode)
    
    if not mres:
        print("❌ Model training failed. Exiting.")
        return
    
    print("✅ Machine learning prediction completed")
    
    # Step 9: Generate results
    print("\n📈 Step 9: Generating results and reports...")
    create_summary_plots(res_df, mres, cfg)
    write_results_to_las(res_df, tgt, las_objs, out)
    generate_final_report(mres, hparams)
    
    print("\n🎉 Workflow completed successfully!")
    print(f"📁 Results saved to: {out}")

def run_original():
    """Run the original workflow with manual path input."""
    from config_handler import get_io_paths
    
    print("=" * 60)
    print(" ML LOG PREDICTION - Original Version")
    print("=" * 60)
    
    inp, out = get_io_paths()
    df, las_objs, wells, logs = load_las_files_from_directory(inp)
    if df.empty:
        return
    feats, tgt = configure_log_selection(logs)
    cfg = configure_well_separation(wells)
    mode = get_prediction_mode()
    hparams = configure_hyperparameters()

    clean_df = clean_log_data(df)
    generate_qc_report(clean_df, feats+[tgt], cfg)

    models = {MODEL_REGISTRY[k]['name']: MODEL_REGISTRY[k]['model_class'](**hparams[k]) for k in MODEL_REGISTRY}
    res_df, mres = impute_logs(clean_df, feats, tgt, models, cfg, mode)
    if not mres: return

    create_summary_plots(res_df, mres, cfg)
    write_results_to_las(res_df, tgt, las_objs, out)
    generate_final_report(mres, hparams)

if __name__ == "__main__":
    print("Choose workflow version:")
    print("1. Enhanced version with file dialog (Recommended)")
    print("2. Original version with manual path input")
    
    while True:
        choice = input("Enter choice (1 or 2): ").strip()
        if choice == "1":
            run_with_dialog()
            break
        elif choice == "2":
            run_original()
            break
        else:
            print("Invalid choice. Please enter 1 or 2.")
