### Project Structure

We will organize the code into the following files:

```
log_imputation_project/
├── main.py                   # The main script to run the workflow
├── ml_core.py                # Contains the ML models registry and the core imputation logic
├── data_handler.py           # Handles LAS file loading, saving, and data cleaning
├── config_handler.py         # Manages all user interaction and configuration
└── reporting.py              # Generates all QC reports and visualizations
```

---

### 1. `ml_core.py` (The Machine Learning Engine)

This is the most important file for the "plug-and-play" requirement. It defines a `MODEL_REGISTRY` where you can easily add or remove models. The core `impute_logs` function is generic and works with any model that follows the scikit-learn `.fit()` and `.predict()` API.

```python
# ml_core.py

import numpy as np
import pandas as pd
from xgboost import XGBRegressor
from lightgbm import LGBMRegressor
from catboost import CatBoostRegressor
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import mean_absolute_error, r2_score, mean_squared_error

# --- PLUG-AND-PLAY MODEL REGISTRY ---
# To add a new model, simply add a new dictionary entry here.
# The config_handler will automatically pick it up for the UI.
MODEL_REGISTRY = {
    'xgboost': {
        'name': 'XGBoost',
        'model_class': XGBRegressor,
        'hyperparameters': {
            'n_estimators': {'type': int, 'default': 300, 'min': 50, 'max': 2000, 'prompt': "Number of estimators (trees)"},
            'learning_rate': {'type': float, 'default': 0.05, 'min': 0.01, 'max': 0.3, 'prompt': "Learning rate"},
            'max_depth': {'type': int, 'default': 6, 'min': 3, 'max': 15, 'prompt': "Max tree depth"},
            'subsample': {'type': float, 'default': 1.0, 'min': 0.5, 'max': 1.0, 'prompt': "Subsample ratio"},
            'colsample_bytree': {'type': float, 'default': 1.0, 'min': 0.5, 'max': 1.0, 'prompt': "Column sampling per tree"},
            'reg_alpha': {'type': float, 'default': 0, 'min': 0, 'max': 10, 'prompt': "L1 regularization (reg_alpha)"},
            'reg_lambda': {'type': float, 'default': 1, 'min': 0, 'max': 10, 'prompt': "L2 regularization (reg_lambda)"},
        },
        'fixed_params': {'random_state': 42, 'early_stopping_rounds': 50},
        'gpu_check': {
            'param': 'tree_method',
            'gpu_value': 'gpu_hist',
            'cpu_value': 'hist'
        }
    },
    'lightgbm': {
        'name': 'LightGBM',
        'model_class': LGBMRegressor,
        'hyperparameters': {
            'n_estimators': {'type': int, 'default': 300, 'min': 50, 'max': 2000, 'prompt': "Number of estimators (trees)"},
            'learning_rate': {'type': float, 'default': 0.1, 'min': 0.01, 'max': 0.3, 'prompt': "Learning rate"},
            'max_depth': {'type': int, 'default': -1, 'min': -1, 'max': 15, 'prompt': "Max tree depth (-1 for no limit)"},
            'min_child_samples': {'type': int, 'default': 20, 'min': 5, 'max': 100, 'prompt': "Min samples in a leaf node"},
        },
        'fixed_params': {'random_state': 42},
        'gpu_check': {
            'param': 'device',
            'gpu_value': 'gpu',
            'cpu_value': 'cpu'
        }
    },
    'catboost': {
        'name': 'CatBoost',
        'model_class': CatBoostRegressor,
        'hyperparameters': {
            'iterations': {'type': int, 'default': 1000, 'min': 100, 'max': 5000, 'prompt': "Number of iterations (trees)"},
            'learning_rate': {'type': float, 'default': None, 'min': 0.01, 'max': 0.3, 'prompt': "Learning rate (leave blank for auto)"},
            'depth': {'type': int, 'default': 6, 'min': 3, 'max': 12, 'prompt': "Tree depth"},
            'l2_leaf_reg': {'type': float, 'default': 3, 'min': 1, 'max': 10, 'prompt': "L2 regularization"},
        },
        'fixed_params': {'random_state': 42, 'verbose': 0, 'early_stopping_rounds': 50},
        'gpu_check': {
            'param': 'task_type',
            'gpu_value': 'GPU',
            'cpu_value': 'CPU'
        }
    }
}

def evaluate_model_comprehensive(model, X_train, y_train, X_val, y_val):
    """Comprehensive model evaluation using multiple metrics."""
    y_pred = model.predict(X_val)
    mae = mean_absolute_error(y_val, y_pred)
    rmse = np.sqrt(mean_squared_error(y_val, y_pred))
    r2 = r2_score(y_val, y_pred)
    
    # Simple composite score (lower is better)
    # Penalizes high error and rewards high R-squared
    r2_penalty = (1 - r2) if r2 > 0 else 10 # Heavily penalize negative R2
    composite_score = (mae * 0.5) + (rmse * 0.3) + (r2_penalty * 0.2)
    
    return {'mae': mae, 'rmse': rmse, 'r2': r2, 'composite_score': composite_score}

def impute_logs(df, feature_cols, target_col, models_to_run, well_config, prediction_mode):
    """
    Core ML imputation function. Trains models, evaluates them, and predicts missing values.
    
    Args:
        df (pd.DataFrame): The combined dataframe of all well data.
        feature_cols (list): List of feature log names.
        target_col (str): The name of the target log to impute.
        models_to_run (dict): A dictionary of instantiated model objects, e.g., {'XGBoost': XGBRegressor(...)}.
        well_config (dict): Configuration for well separation.
        prediction_mode (int): The prediction mode (1, 2, or 3).

    Returns:
        tuple: A tuple containing (results_df, model_results_dict).
    """
    res = df.copy()
    feature_set = feature_cols + ['MD']
    
    print(f'\n--- Processing Target Log: {target_col} ---')

    # 1. Prepare Training Data based on well separation
    if well_config['mode'] == 'separated':
        training_well_names = well_config.get('training_wells', [])
        train_df = res[res['WELL'].isin(training_well_names)]
        train = train_df[train_df[target_col].notna()].copy()
        print(f"Using {len(train)} samples from {len(training_well_names)} designated training wells.")
    else: # Mixed mode
        train = res[res[target_col].notna()].copy()
        print(f"Using {len(train)} samples from all wells for training (Mixed Mode).")
    
    if len(train) < 50:
        print(f"WARNING: Low number of training samples ({len(train)}). Model performance may be poor.")
        if len(train) == 0:
            print("ERROR: No training data available. Skipping this target.")
            return res, {}

    # 2. Prepare data for scikit-learn
    X = train[feature_set].apply(lambda c: c.fillna(c.mean()), axis=0)
    y = train[target_col]
    X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=0.25, random_state=42)

    # 3. Train and Evaluate Models
    model_evaluations = []
    trained_models = {}
    print(f'Training {len(models_to_run)} models...')
    for name, model in models_to_run.items():
        try:
            print(f'  Training {name}...', end=' ')
            # Use eval_set for models that support early stopping
            if 'early_stopping_rounds' in model.get_params():
                 model.fit(X_train, y_train, eval_set=[(X_val, y_val)], verbose=False)
            else:
                 model.fit(X_train, y_train)

            evaluation = evaluate_model_comprehensive(model, X_train, y_train, X_val, y_val)
            evaluation['model_name'] = name
            model_evaluations.append(evaluation)
            trained_models[name] = model
            print(f'✓  MAE: {evaluation["mae"]:.3f}, R²: {evaluation["r2"]:.3f}')
        except Exception as e:
            print(f'FAILED ✗. Error: {e}')

    if not model_evaluations:
        print("ERROR: All models failed to train. Aborting.")
        return res, {}

    # 4. Select Best Model and Generate Predictions
    model_evaluations.sort(key=lambda x: x['composite_score'])
    best_model_eval = model_evaluations[0]
    best_model_name = best_model_eval['model_name']
    best_model = trained_models[best_model_name]
    
    print(f'\n--- Model Performance Ranking ---')
    for i, eval_result in enumerate(model_evaluations, 1):
        rank_symbol = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else f"{i}."
        print(f'  {rank_symbol} {eval_result["model_name"]} (Score: {eval_result["composite_score"]:.3f})')
        
    print(f'\nBest model selected: {best_model_name}')

    # 5. Determine which rows to predict on
    if well_config['mode'] == 'separated':
        prediction_well_names = well_config.get('prediction_wells', [])
        prediction_mask = res['WELL'].isin(prediction_well_names)
        print(f"Generating predictions for {prediction_mask.sum()} rows in {len(prediction_well_names)} prediction wells.")
    else: # Mixed mode
        prediction_mask = pd.Series(True, index=res.index) # Predict on all rows
        print("Generating predictions for all rows (Mixed Mode).")
    
    X_pred = res.loc[prediction_mask, feature_set].apply(lambda c: c.fillna(c.mean()), axis=0)
    
    if not X_pred.empty:
        predictions = best_model.predict(X_pred)
        full_predictions = pd.Series(np.nan, index=res.index)
        full_predictions.loc[prediction_mask] = predictions
    else:
        full_predictions = pd.Series(np.nan, index=res.index)

    # 6. Apply prediction mode logic and create new columns
    imputed_col_name = f'{target_col}_imputed'
    pred_col_name = f'{target_col}_pred'
    error_col_name = f'{target_col}_error'
    
    res[pred_col_name] = full_predictions
    
    if prediction_mode == 1: # Fill Missing Only
        res[imputed_col_name] = res[target_col].fillna(full_predictions)
    elif prediction_mode == 2: # Cross-Validation (handled in training, here same as fill)
        res[imputed_col_name] = res[target_col].fillna(full_predictions)
    elif prediction_mode == 3: # Re-predict Entire Log
        res[imputed_col_name] = np.nan
        res.loc[prediction_mask, imputed_col_name] = full_predictions.loc[prediction_mask]

    # Calculate relative error where original data exists
    original_mask = res[target_col].notna() & res[pred_col_name].notna()
    res[error_col_name] = np.nan
    res.loc[original_mask, error_col_name] = np.abs(res.loc[original_mask, target_col] - res.loc[original_mask, pred_col_name])

    model_results = {
        'target': target_col,
        'evaluations': model_evaluations,
        'best_model_name': best_model_name,
        'trained_models': trained_models
    }
    
    return res, model_results
```

---

### 2. `data_handler.py` (File I/O and Cleaning)

This module encapsulates all interactions with the file system.

```python
# data_handler.py

import os
import glob
import lasio
import pandas as pd
import numpy as np

def load_las_files_from_directory(input_dir):
    """Loads all LAS files from a directory into a single DataFrame."""
    las_files = glob.glob(os.path.join(input_dir, '*.las'))
    if not las_files:
        print(f"ERROR: No .las files found in directory: {input_dir}")
        return pd.DataFrame(), {}, [], []

    all_data = []
    las_objects = {}
    well_names = []

    print(f"Found {len(las_files)} LAS files. Loading...")
    for f in las_files:
        try:
            las = lasio.read(f)
            well_name = las.well.WELL.value
            if not well_name:
                well_name = os.path.splitext(os.path.basename(f))[0]
            
            df = las.to_df().reset_index()
            # Find the depth column, common names are DEPT, DEPTH, MD
            depth_col = next((col for col in ['DEPT', 'DEPTH', 'MD'] if col in df.columns), None)
            if not depth_col:
                print(f"  - WARNING: No standard depth column found in {well_name}. Skipping.")
                continue
            
            df.rename(columns={depth_col: 'MD'}, inplace=True)
            df['WELL'] = well_name
            
            all_data.append(df)
            las_objects[well_name] = las
            well_names.append(well_name)
            print(f"  - Loaded {well_name} ({len(df)} rows)")
        except Exception as e:
            print(f"  - ERROR loading file {f}: {e}")

    if not all_data:
        return pd.DataFrame(), {}, [], []

    combined_df = pd.concat(all_data, ignore_index=True)
    log_names = [col for col in combined_df.columns if col not in ['MD', 'WELL']]
    
    return combined_df, las_objects, sorted(list(set(well_names))), sorted(log_names)

def clean_log_data(df):
    """Applies basic cleaning and range clipping to log data."""
    print("\nApplying basic data cleaning...")
    clean_df = df.copy()
    cleaning_rules = {
        'GR': (0, 300),
        'NPHI': (0, 1),
        'RHOB': (1.5, 3.0),
        'DT': (40, 200)
    }
    for col, (min_val, max_val) in cleaning_rules.items():
        if col in clean_df.columns:
            original_nans = clean_df[col].isna().sum()
            clean_df[col] = np.where((clean_df[col] >= min_val) & (clean_df[col] <= max_val), clean_df[col], np.nan)
            new_nans = clean_df[col].isna().sum()
            print(f"  - Cleaned '{col}'. Invalid values replaced with NaN: {new_nans - original_nans}")
    return clean_df
    
def write_results_to_las(results_df, target_log, las_objects, output_dir):
    """Writes the imputed logs back to new LAS files."""
    print(f"\nWriting results to new LAS files in: {output_dir}")
    os.makedirs(output_dir, exist_ok=True)
    
    imputed_col = f'{target_log}_imputed'
    pred_col = f'{target_log}_pred'
    error_col = f'{target_log}_error'
    
    for well_name, las in las_objects.items():
        well_df = results_df[results_df['WELL'] == well_name]
        if well_df.empty:
            continue
            
        new_las = las.copy()
        
        # Remove old versions of these curves if they exist
        for col_name in [imputed_col, pred_col, error_col]:
             if col_name in new_las.keys():
                 new_las.delete_curve(col_name)

        for col_name, unit, descr in [
            (imputed_col, las[target_log].unit, f"ML Imputed {target_log}"),
            (pred_col, las[target_log].unit, f"ML Predicted {target_log}"),
            (error_col, las[target_log].unit, f"Prediction Error for {target_log}")
        ]:
            if col_name in well_df.columns:
                curve_data = well_df.set_index('MD')[col_name].reindex(new_las.index).values
                new_curve = lasio.CurveItem(col_name, unit=unit, descr=descr, data=curve_data)
                new_las.append_curve_item(new_curve)

        output_path = os.path.join(output_dir, f"{well_name}_imputed.las")
        new_las.write(output_path, version=2.0)
        print(f"  - Saved {output_path}")

```
---

### 3. `config_handler.py` (User Interface)

This file handles all command-line interactions for setting up the workflow.

```python
# config_handler.py

import os
from ml_core import MODEL_REGISTRY

AUTO_MODE = False # Set to True to skip all prompts and use defaults

def get_io_paths():
    """Prompts user for input and output directories."""
    print("\n--- Step 1: Specify Input and Output Directories ---")
    while True:
        input_dir = input("Enter the path to the directory containing your LAS files: ").strip()
        if os.path.isdir(input_dir):
            break
        print("ERROR: Directory not found. Please enter a valid path.")
        
    while True:
        output_dir = input("Enter the path for the output directory (will be created if it doesn't exist): ").strip()
        if output_dir:
            break
        print("ERROR: Output directory cannot be empty.")
        
    return input_dir, output_dir

def console_select(options, prompt, default=None, multiple=False, auto_mode=False):
    """Unified console selection function."""
    if not options: return [] if multiple else None
    
    print(f"\n{prompt}")
    for i, option in enumerate(options, 1):
        print(f"  {i}. {option}")
    
    default_indices_str = ""
    if default:
        default_indices = [options.index(d) + 1 for d in default if d in options] if multiple else [options.index(default) + 1]
        if default_indices:
            default_indices_str = ', '.join(map(str, default_indices))
            print(f"Default: {default_indices_str}")

    if auto_mode:
        selected_indices = default_indices if default else [1]
        result = [options[i - 1] for i in selected_indices]
        print(f"Auto-selected: {result if multiple else result[0]}")
        return result if multiple else result[0]

    while True:
        user_input = input(f"Enter selection(s) by number (e.g., 1,3 or 'all') or press Enter for default: ").strip()
        if not user_input and default_indices_str:
            selected_indices = default_indices
            break
        elif user_input.lower() == 'all' and multiple:
            selected_indices = list(range(1, len(options) + 1))
            break
        try:
            selected_indices = [int(x.strip()) for x in user_input.split(',')]
            if all(1 <= i <= len(options) for i in selected_indices):
                if not multiple and len(selected_indices) > 1:
                    print("Please select only one option.")
                    continue
                break
            else:
                print(f"Invalid selection. Please enter numbers between 1 and {len(options)}.")
        except ValueError:
            print("Invalid input. Please enter numbers.")
            
    result = [options[i-1] for i in selected_indices]
    print(f"Selected: {result if multiple else result[0]}")
    return result if multiple else result[0]

def get_int_input(prompt, default, min_val, max_val):
    while True:
        try:
            val_str = input(f"{prompt} (default: {default}, range: {min_val}-{max_val}): ").strip()
            if not val_str: return default
            val = int(val_str)
            if min_val <= val <= max_val: return val
            print(f"Value must be between {min_val} and {max_val}.")
        except ValueError: print("Please enter a valid integer.")

def get_float_input(prompt, default, min_val, max_val):
    while True:
        try:
            val_str = input(f"{prompt} (default: {default}, range: {min_val}-{max_val}): ").strip()
            if not val_str: return default
            if default is None and not val_str: return None # Handle optional floats
            val = float(val_str)
            if min_val <= val <= max_val: return val
            print(f"Value must be between {min_val} and {max_val}.")
        except ValueError: print("Please enter a valid number.")

def configure_log_selection(log_names):
    print("\n--- Step 2: Select Feature and Target Logs ---")
    default_features = [l for l in ['GR', 'RHOB', 'NPHI', 'DT'] if l in log_names] or log_names[:min(4, len(log_names))]
    feature_logs = console_select(log_names, "Select INPUT (feature) logs:", default=default_features, multiple=True, auto_mode=AUTO_MODE)
    
    available_targets = [l for l in log_names if l not in feature_logs]
    default_target = ('VS' if 'VS' in available_targets else 'DTS' if 'DTS' in available_targets else available_targets[0]) if available_targets else None
    target_log = console_select(available_targets, "Select TARGET log to predict:", default=default_target, auto_mode=AUTO_MODE)
    
    return feature_logs, target_log

def configure_well_separation(well_names):
    print("\n--- Step 3: Configure Well Separation (for Training vs. Prediction) ---")
    mode = console_select(['Mixed Mode (All wells for training & prediction)', 'Separated Mode (Designate specific wells)'], "Select mode:", 'Mixed Mode (All wells for training & prediction)', auto_mode=AUTO_MODE)
    
    if 'Mixed' in mode:
        return {'mode': 'mixed', 'training_wells': well_names, 'prediction_wells': well_names}
    
    training_wells = console_select(well_names, "Select wells for TRAINING:", multiple=True, auto_mode=AUTO_MODE)
    remaining_wells = [w for w in well_names if w not in training_wells]
    prediction_wells = console_select(remaining_wells, "Select wells for PREDICTION:", multiple=True, default=remaining_wells, auto_mode=AUTO_MODE)
    
    return {'mode': 'separated', 'training_wells': training_wells, 'prediction_wells': prediction_wells}

def get_prediction_mode():
    print("\n--- Step 4: Select Prediction Mode ---")
    modes = {1: "Fill Missing Values Only", 2: "Cross-Validation Test", 3: "Re-predict Entire Log"}
    mode_options = [f"{k}: {v}" for k, v in modes.items()]
    selected_str = console_select(mode_options, "Select a prediction mode:", default="1: Fill Missing Values Only", auto_mode=AUTO_MODE)
    return int(selected_str.split(':')[0])

def configure_hyperparameters():
    print("\n--- Step 5: Configure Model Hyperparameters ---")
    
    config_choice = console_select(["Use optimized defaults", "Configure manually"], "Select hyperparameter configuration:", "Use optimized defaults", auto_mode=AUTO_MODE)
    
    if "Use optimized defaults" in config_choice:
        auto_mode = True
    else:
        auto_mode = False

    final_params = {}
    for key, model_def in MODEL_REGISTRY.items():
        print(f"\n--- {model_def['name']} Configuration ---")
        model_params = {}
        # Auto-detect GPU
        gpu_info = model_def.get('gpu_check')
        if gpu_info:
            try:
                # A simple test to see if GPU is usable
                if key == 'xgboost': import xgboost as xgb; xgb.XGBRegressor(tree_method='gpu_hist', n_estimators=1).fit([[1]], [1])
                elif key == 'lightgbm': import lightgbm as lgb; lgb.LGBMRegressor(device='gpu', n_estimators=1).fit([[1]], [1])
                elif key == 'catboost': import catboost as cb; cb.CatBoostRegressor(task_type='GPU', iterations=1, verbose=0).fit([[1]], [1])
                model_params[gpu_info['param']] = gpu_info['gpu_value']
                print("✓ GPU detected and enabled.")
            except:
                model_params[gpu_info['param']] = gpu_info['cpu_value']
                print("⚠ GPU not available or configured, using CPU.")
        
        # Get user input for tunable params
        for param_name, P in model_def['hyperparameters'].items():
            if auto_mode:
                model_params[param_name] = P['default']
            else:
                if P['type'] == int:
                    model_params[param_name] = get_int_input(P['prompt'], P['default'], P['min'], P['max'])
                elif P['type'] == float:
                    model_params[param_name] = get_float_input(P['prompt'], P['default'], P['min'], P['max'])

        # Add fixed params
        model_params.update(model_def.get('fixed_params', {}))
        final_params[key] = model_params
    
    print("\n--- Final Hyperparameter Summary ---")
    for model_key, params in final_params.items():
        print(f"  {MODEL_REGISTRY[model_key]['name']}: {params}")
        
    return final_params

```
---

### 4. `reporting.py` (QC and Visualization)

This module is responsible for creating all the output plots and text reports.

```python
# reporting.py

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import mean_absolute_error, r2_score

def generate_qc_report(df, log_names, well_config):
    """Generate and print a data coverage QC report."""
    print("\n--- QC Report: Data Coverage ---")
    
    # Overall coverage
    coverage = 1.0 - df[log_names].isna().mean()
    print("\n1. Overall Data Coverage:")
    for log, cov in coverage.items():
        status = "✓" if cov > 0.7 else "⚠" if cov > 0.3 else "✗"
        print(f"  {status} {log}: {cov:.1%}")

    # Per-well coverage
    print("\n2. Per-Well Data Coverage:")
    for well in df['WELL'].unique():
        well_df = df[df['WELL'] == well]
        well_type = "Train" if well in well_config.get('training_wells', []) else "Pred"
        well_cov = 1.0 - well_df[log_names].isna().mean()
        target_cov = well_cov.get(log_names[-1], 0) # Assumes last log is target
        status = "✓" if target_cov > 0.7 else "⚠" if target_cov > 0.3 else "✗"
        print(f"  {status} Well '{well}' ({well_type}): Target coverage = {target_cov:.1%}")

def create_summary_plots(results_df, model_results, well_config):
    """Creates summary plots for the imputation results."""
    target_log = model_results['target']
    best_model_name = model_results['best_model_name']
    
    print(f"\nGenerating summary plots for target '{target_log}'...")
    
    well_names = results_df['WELL'].unique()
    num_wells = len(well_names)
    
    # Determine which wells to plot
    if well_config['mode'] == 'separated':
        wells_to_plot = well_config['prediction_wells']
        title_suffix = "(Prediction Wells)"
    else:
        wells_to_plot = well_names
        title_suffix = "(All Wells)"
    
    # Limit plots to a reasonable number
    if len(wells_to_plot) > 6:
        print(f"Limiting plot to first 6 of {len(wells_to_plot)} wells for clarity.")
        wells_to_plot = wells_to_plot[:6]

    if not wells_to_plot:
        print("No wells designated for plotting.")
        return

    num_plots = len(wells_to_plot)
    fig, axes = plt.subplots(1, num_plots, figsize=(4 * num_plots, 10), sharey=True)
    if num_plots == 1: axes = [axes]

    for i, well_name in enumerate(wells_to_plot):
        ax = axes[i]
        well_data = results_df[results_df['WELL'] == well_name]
        
        # Plot original data
        ax.plot(well_data[target_log], well_data['MD'], 'k-', label='Original', lw=2)
        # Plot imputed data
        ax.plot(well_data[f'{target_log}_imputed'], well_data['MD'], 'r--', label='Imputed', lw=2)
        
        # Calculate metrics if original data exists for comparison
        compare_df = well_data[[target_log, f'{target_log}_imputed']].dropna()
        if not compare_df.empty:
            mae = mean_absolute_error(compare_df[target_log], compare_df[f'{target_log}_imputed'])
            r2 = r2_score(compare_df[target_log], compare_df[f'{target_log}_imputed'])
            ax.text(0.05, 0.02, f'MAE: {mae:.2f}\nR²: {r2:.2f}', transform=ax.transAxes, 
                    bbox=dict(boxstyle='round,pad=0.3', fc='yellow', alpha=0.5))
        
        ax.set_title(well_name)
        ax.set_xlabel(target_log)
        ax.grid(True, linestyle=':', alpha=0.6)
        if i == 0:
            ax.set_ylabel('MD')
            ax.legend()
        ax.invert_yaxis()

    fig.suptitle(f"Imputation Results for '{target_log}' using {best_model_name} {title_suffix}", fontsize=16, y=0.95)
    plt.tight_layout(rect=[0, 0, 1, 0.93])
    plt.show()

def generate_final_report(model_results, hyperparams):
    """Prints a final summary of the modeling results."""
    print("\n" + "="*80)
    print(" " * 25 + "WORKFLOW FINAL SUMMARY REPORT")
    print("="*80)
    
    target = model_results['target']
    evaluations = model_results['evaluations']
    
    print(f"\n🎯 Target Log: {target}")
    
    print("\n📊 Model Performance Ranking:")
    for i, eval_result in enumerate(evaluations, 1):
        rank_symbol = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else f"{i}."
        print(f"  {rank_symbol} {eval_result['model_name']:<10} | MAE: {eval_result['mae']:.4f} | R²: {eval_result['r2']:.4f} | RMSE: {eval_result['rmse']:.4f}")

    print("\n⚙️ Hyperparameters Used:")
    for model_key, params in hyperparams.items():
        print(f"  - {MODEL_REGISTRY[model_key]['name']}: {params}")
        
    print("\n✅ Workflow complete. Results saved to the specified output directory.")
    print("="*80)

```
---

### 5. `main.py` (The Orchestrator)

This script ties everything together. It's clean, high-level, and easy to follow.

```python
# main.py

# Import all necessary functions from our modules
from data_handler import load_las_files_from_directory, clean_log_data, write_results_to_las
from config_handler import get_io_paths, configure_log_selection, configure_well_separation, get_prediction_mode, configure_hyperparameters
from ml_core import impute_logs, MODEL_REGISTRY
from reporting import generate_qc_report, create_summary_plots, generate_final_report

def run_workflow():
    """Main function to orchestrate the entire log imputation workflow."""
    print("="*60)
    print("      Machine Learning Well Log Imputation Workflow")
    print("="*60)

    # 1. Configuration
    input_dir, output_dir = get_io_paths()
    
    # 2. Data Loading and Initial Setup
    well_data, las_objects, well_names, log_names = load_las_files_from_directory(input_dir)
    if well_data.empty:
        print("Exiting: No data was loaded.")
        return

    feature_logs, target_log = configure_log_selection(log_names)
    if not target_log:
        print("Exiting: No target log selected.")
        return

    well_config = configure_well_separation(well_names)
    prediction_mode = get_prediction_mode()
    hyperparams = configure_hyperparameters()
    
    # 3. Data Processing
    cleaned_data = clean_log_data(well_data)
    generate_qc_report(cleaned_data, feature_logs + [target_log], well_config)
    
    # 4. Machine Learning Core
    # Instantiate selected models with configured hyperparameters
    models_to_run = {}
    for key, model_def in MODEL_REGISTRY.items():
        params = hyperparams.get(key, {})
        models_to_run[model_def['name']] = model_def['model_class'](**params)
        
    results_df, model_results = impute_logs(
        df=cleaned_data,
        feature_cols=feature_logs,
        target_col=target_log,
        models_to_run=models_to_run,
        well_config=well_config,
        prediction_mode=prediction_mode
    )
    
    if not model_results:
        print("\nExiting: Machine learning process failed.")
        return
        
    # 5. Reporting and Visualization
    create_summary_plots(results_df, model_results, well_config)
    
    # 6. Writing Results
    write_results_to_las(results_df, target_log, las_objects, output_dir)
    
    # 7. Final Summary
    generate_final_report(model_results, hyperparams)

if __name__ == "__main__":
    run_workflow()

```

### How to Use the New Structure

1.  **Save the Files:** Create a directory named `log_imputation_project` and save each of the five code blocks above into their respective `.py` files inside it.
2.  **Install Dependencies:**
    ```bash
    pip install pandas numpy scikit-learn xgboost lightgbm catboost lasio matplotlib
    ```
3.  **Run the Main Script:** Open a terminal, navigate to the `log_imputation_project` directory, and run:
    ```bash
    python main.py
    ```
4.  Follow the on-screen prompts to specify your directories and make your selections.

### How to Add a New Machine Learning Model (Plug-and-Play)

Let's say you want to add `sklearn.ensemble.RandomForestRegressor`.

1.  **Open `ml_core.py`**.
2.  **Add an entry to `MODEL_REGISTRY`**:

    ```python
    # ml_core.py

    # ... existing imports
    from sklearn.ensemble import RandomForestRegressor 

    MODEL_REGISTRY = {
        # ... existing entries for xgboost, lightgbm, catboost
        
        'random_forest': {
            'name': 'Random Forest',
            'model_class': RandomForestRegressor,
            'hyperparameters': {
                'n_estimators': {'type': int, 'default': 100, 'min': 10, 'max': 500, 'prompt': "Number of trees in the forest"},
                'max_depth': {'type': int, 'default': None, 'min': 3, 'max': 50, 'prompt': "Maximum depth of the tree (blank for none)"},
                'min_samples_leaf': {'type': int, 'default': 1, 'min': 1, 'max': 20, 'prompt': "Minimum number of samples required to be at a leaf node"},
            },
            'fixed_params': {'random_state': 42, 'n_jobs': -1}, # n_jobs=-1 uses all CPU cores
            # No GPU check needed for this scikit-learn model
        }
    }
    # ... rest of the file
    ```

That's it! The next time you run `main.py`, "Random Forest" will automatically appear as a configurable option in the hyperparameter setup step, and it will be trained and evaluated alongside the other models. This modular design makes the tool incredibly flexible and easy to extend.