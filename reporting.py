import matplotlib.pyplot as plt
from sklearn.metrics import mean_absolute_error, r2_score

def generate_qc_report(df, logs, cfg):
    print("\nCoverage:")
    cov = 1 - df[logs].isna().mean()
    for l,c in cov.items():
        print(f"  {l}: {c:.1%}")

def create_summary_plots(res_df, model_res, cfg):
    tgt = model_res['target']
    wells = cfg['prediction_wells'] if cfg['mode']=='separated' else res_df['WELL'].unique()
    wells = wells[:6]
    fig, axes = plt.subplots(1,len(wells), figsize=(4*len(wells),8), sharey=True)
    if len(wells)==1:
        axes=[axes]
    for ax,w in zip(axes,wells):
        d = res_df[res_df['WELL']==w]
        ax.plot(d[tgt], d['MD'], 'k-', label='Orig')
        ax.plot(d[f"{tgt}_imputed"], d['MD'], 'r--', label='Imp')
        comp = d[[tgt,f"{tgt}_imputed"]].dropna()
        if not comp.empty:
            mae = mean_absolute_error(comp[tgt], comp[f"{tgt}_imputed"])
            r2 = r2_score(comp[tgt], comp[f"{tgt}_imputed"])
            ax.set_title(f"{w}\nMAE={mae:.2f} R2={r2:.2f}")
        else:
            ax.set_title(w)
        ax.invert_yaxis()
    axes[0].set_ylabel('MD')
    axes[0].legend()
    plt.tight_layout()
    plt.show()

def generate_final_report(model_res, hparams):
    print("\n=== FINAL REPORT ===")
    print(f"Target: {model_res['target']}")
    for i,e in enumerate(model_res['evaluations'],1):
        print(f"{i}. {e['model_name']} MAE={e['mae']:.3f} R2={e['r2']:.3f}")
