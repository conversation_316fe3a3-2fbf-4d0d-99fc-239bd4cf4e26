#!/usr/bin/env python3
"""
Simple validation of the lasio fix.
"""

import os
import sys

def validate_lasio_fix():
    """Validate that the lasio fix works."""
    try:
        print("Step 1: Testing lasio import...")
        import lasio
        print(f"✅ lasio version: {lasio.__version__}")
        
        print("\nStep 2: Testing LAS file loading...")
        test_file = "C:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/BKP Main Log/RP_OUTPUT/B-G-6_RP_INPUT.las"
        
        if not os.path.exists(test_file):
            print(f"❌ Test file not found: {test_file}")
            return False
            
        las = lasio.read(test_file)
        print(f"✅ LAS file loaded successfully")
        
        print("\nStep 3: Testing DataFrame conversion...")
        df = las.df()
        print(f"✅ DataFrame conversion successful: {df.shape}")
        
        print("\nStep 4: Testing data_handler import...")
        from data_handler import load_las_files_from_directory
        print("✅ data_handler imported successfully")
        
        print("\nStep 5: Testing data_handler with single file...")
        df, las_objs, wells, logs = load_las_files_from_directory([test_file])
        
        if not df.empty:
            print(f"✅ data_handler works! Loaded {len(df)} rows, {len(wells)} wells")
            print(f"   Wells: {wells}")
            print(f"   Logs: {logs[:5]}{'...' if len(logs) > 5 else ''}")
            return True
        else:
            print("❌ data_handler returned empty DataFrame")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Validating lasio fix...")
    print("=" * 50)
    
    success = validate_lasio_fix()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 ALL TESTS PASSED! The fix is working correctly.")
        print("\nYou can now run your main script:")
        print("   C:\\Users\\<USER>\\codellm\\Scripts\\python.exe main_with_dialog.py")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    
    sys.exit(0 if success else 1)
