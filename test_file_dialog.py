#!/usr/bin/env python3
"""
Test script to demonstrate the LAS file selection dialog functionality.
"""

from config_handler import select_las_files_dialog, get_io_paths_simple
from data_handler import load_las_files_from_directory

def test_file_selection():
    """Test the file selection dialog."""
    print("=== Testing LAS File Selection Dialog ===")
    
    # Test 1: Direct file selection
    print("\n1. Testing direct file selection...")
    selected_files = select_las_files_dialog()
    
    if selected_files:
        print(f"\nSuccessfully selected {len(selected_files)} files:")
        for i, file_path in enumerate(selected_files, 1):
            print(f"  {i}. {file_path}")
        
        # Test loading the selected files
        print("\n2. Testing file loading...")
        df, las_objs, wells, logs = load_las_files_from_directory(selected_files)
        
        if not df.empty:
            print(f"✓ Successfully loaded data:")
            print(f"  - Total rows: {len(df)}")
            print(f"  - Wells: {wells}")
            print(f"  - Log curves: {logs}")
        else:
            print("✗ Failed to load data from selected files")
    else:
        print("No files selected.")

def test_io_paths():
    """Test the complete I/O path selection."""
    print("\n=== Testing Complete I/O Path Selection ===")
    
    inp, out = get_io_paths_simple()
    
    if inp and out:
        print(f"\nInput: {inp}")
        print(f"Output: {out}")
        
        # Test loading
        df, las_objs, wells, logs = load_las_files_from_directory(inp)
        if not df.empty:
            print(f"✓ Data loaded successfully: {len(df)} rows, {len(wells)} wells")
        else:
            print("✗ Failed to load data")
    else:
        print("Selection cancelled.")

if __name__ == "__main__":
    print("LAS File Selection Dialog Test")
    print("=" * 40)
    
    while True:
        print("\nChoose test:")
        print("1. Test file selection dialog only")
        print("2. Test complete I/O path selection")
        print("3. Exit")
        
        choice = input("Enter choice (1-3): ").strip()
        
        if choice == "1":
            test_file_selection()
        elif choice == "2":
            test_io_paths()
        elif choice == "3":
            print("Exiting...")
            break
        else:
            print("Invalid choice. Please enter 1, 2, or 3.")
