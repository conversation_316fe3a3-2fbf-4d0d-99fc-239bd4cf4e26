#!/usr/bin/env python3
"""
Simple demo of the LAS file selection dialog.
"""

from config_handler import select_las_files_dialog

def demo():
    """Demonstrate the file selection dialog."""
    print("Demo: LAS File Selection Dialog")
    print("=" * 40)
    print("This will open a file dialog where you can select multiple LAS files.")
    print("Hold Ctrl (or Cmd on Mac) to select multiple files.")
    print()
    
    input("Press Enter to open the file dialog...")
    
    # Open the file selection dialog
    selected_files = select_las_files_dialog()
    
    if selected_files:
        print(f"\n✅ You selected {len(selected_files)} files:")
        for i, file_path in enumerate(selected_files, 1):
            print(f"  {i}. {file_path}")
        
        print(f"\nThese files can now be processed by the ML workflow.")
    else:
        print("\n❌ No files were selected.")
    
    print("\nDemo completed!")

if __name__ == "__main__":
    demo()
