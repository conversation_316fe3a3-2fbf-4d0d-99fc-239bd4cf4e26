#!/usr/bin/env python3
"""
Test script to verify the lasio DataFrame conversion fix.
"""

import os
import lasio
import pandas as pd

def test_lasio_conversion():
    """Test different methods to convert LAS to DataFrame."""
    
    # Test with one of your LAS files
    test_file = "C:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/BKP Main Log/RP_OUTPUT/B-G-6_RP_INPUT.las"
    
    if not os.path.exists(test_file):
        print(f"Test file not found: {test_file}")
        print("Please update the test_file path to point to one of your LAS files.")
        return
    
    try:
        print(f"Testing LAS file: {os.path.basename(test_file)}")
        las = lasio.read(test_file)
        print(f"✅ Successfully loaded LAS file")
        print(f"   - Well name: {las.well.WELL.value if las.well.WELL.value else 'Not specified'}")
        print(f"   - Number of curves: {len(las.curves)}")
        print(f"   - Curve names: {[curve.mnemonic for curve in las.curves]}")
        
        # Test DataFrame conversion methods
        df = None
        method_used = None
        
        if hasattr(las, 'to_df') and callable(las.to_df):
            try:
                df = las.to_df()
                method_used = "to_df()"
                print(f"✅ Successfully converted using {method_used}")
            except Exception as e:
                print(f"❌ to_df() failed: {e}")
        
        if df is None and hasattr(las, 'df') and callable(las.df):
            try:
                df = las.df()
                method_used = "df()"
                print(f"✅ Successfully converted using {method_used}")
            except Exception as e:
                print(f"❌ df() failed: {e}")
        
        if df is None:
            try:
                df = las.df
                method_used = "df property"
                print(f"✅ Successfully converted using {method_used}")
            except Exception as e:
                print(f"❌ df property failed: {e}")
        
        if df is not None:
            print(f"✅ DataFrame created successfully:")
            print(f"   - Shape: {df.shape}")
            print(f"   - Columns: {list(df.columns)}")
            print(f"   - Index name: {df.index.name}")
            
            # Reset index to get depth as a column
            df_reset = df.reset_index()
            print(f"   - After reset_index: {list(df_reset.columns)}")
            
            # Check for depth columns
            depth_cols = [c for c in df_reset.columns if c in ['DEPT', 'DEPTH', 'MD']]
            print(f"   - Depth columns found: {depth_cols}")
            
        else:
            print("❌ Failed to convert LAS to DataFrame using any method")
            
    except Exception as e:
        print(f"❌ Error loading LAS file: {e}")

if __name__ == "__main__":
    test_lasio_conversion()
